#!/usr/bin/env python3
"""
将image文件的affine矩阵复制到label文件
"""

import nibabel as nib
import numpy as np
import os

def copy_affine(image_path, label_path):
    """将image文件的affine矩阵复制到label文件"""
    try:
        # 检查文件是否存在
        if not os.path.exists(image_path):
            print(f"错误: Image文件不存在: {image_path}")
            return False
            
        if not os.path.exists(label_path):
            print(f"错误: Label文件不存在: {label_path}")
            return False
        
        # 加载文件
        print(f"加载image文件: {image_path}")
        image_nii = nib.load(image_path)
        
        print(f"加载label文件: {label_path}")
        label_nii = nib.load(label_path)
        
        # 获取image的affine矩阵
        image_affine = image_nii.affine
        label_affine = label_nii.affine
        
        print("原始affine矩阵:")
        print("Image affine:")
        print(image_affine)
        print("\nLabel affine:")
        print(label_affine)
        
        # # 检查是否已经相同
        # if np.allclose(image_affine, label_affine, rtol=1e-5):
        #     print("\nAffine矩阵已经相同，无需修改")
        #     return True
        
        # 创建新的label文件，使用image的affine矩阵
        print("\n复制affine矩阵...")
        new_label_nii = nib.Nifti1Image(
            label_nii.get_fdata(),
            affine=image_affine,
            header=label_nii.header
        )
        
        # 保存文件
        print(f"保存修改后的label文件: {label_path}")
        nib.save(new_label_nii, label_path)
        
        # 验证修改
        print("\n验证修改结果...")
        verify_nii = nib.load(label_path)
        if np.allclose(verify_nii.affine, image_affine, rtol=1e-5):
            print("✓ Affine矩阵复制成功！")
            return True
        else:
            print("✗ Affine矩阵复制失败！")
            return False
            
    except Exception as e:
        print(f"操作失败: {str(e)}")
        return False

if __name__ == "__main__":
    image_path = "/home/<USER>/DiffTumor/data/HealthyCT/CT/05_AbdomenAtlas/AbdomenAtlasMini_00002385.nii.gz"
    label_path = "/home/<USER>/DiffTumor/data/HealthyCT/label/liver_label/AbdomenAtlasMini_00002385.nii.gz"
    
    print("=" * 80)
    print("复制Affine矩阵")
    print("=" * 80)
    
    success = copy_affine(image_path, label_path)
    
    if success:
        print("\n操作完成！")
    else:
        print("\n操作失败！")
