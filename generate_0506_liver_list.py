#!/usr/bin/env python3
"""
Script to generate 0506_liver.txt file mapping CT images to liver labels
for AbdomenAtlas (05) and FLARE2023 (06) datasets.
"""

import os
import glob

def main():
    output_file = "STEP3.SegmentationModel/cross_eval/0506_liver.txt"
    
    # Create output directory if it doesn't exist
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    lines = []
    
    # Process AbdomenAtlas files (05)
    abdomen_ct_dir = "data/HealthyCT/CT/05_AbdomenAtlas"
    abdomen_ct_files = glob.glob(os.path.join(abdomen_ct_dir, "*.nii.gz"))
    abdomen_ct_files.sort()
    
    print(f"Found {len(abdomen_ct_files)} AbdomenAtlas CT files")
    
    for ct_file in abdomen_ct_files:
        filename = os.path.basename(ct_file)
        # CT path relative to data root
        ct_path = f"CT/05_AbdomenAtlas/{filename}"
        # Label path - same filename in liver_label directory
        label_path = f"label/liver_label/{filename}"
        
        lines.append(f"{ct_path}      {label_path}")
    
    # Process FLARE2023 files (06)
    flare_ct_dir = "data/HealthyCT/CT/06_FLARE2023"
    flare_ct_files = glob.glob(os.path.join(flare_ct_dir, "*.nii.gz"))
    flare_ct_files.sort()
    
    print(f"Found {len(flare_ct_files)} FLARE2023 CT files")
    
    for ct_file in flare_ct_files:
        filename = os.path.basename(ct_file)
        # Extract number from FLARE23_XXXX_0000.nii.gz format
        if filename.startswith("FLARE23_") and filename.endswith("_0000.nii.gz"):
            # Extract the number part (e.g., "0001" from "FLARE23_0001_0000.nii.gz")
            number_part = filename.split("_")[1]
            # Convert to integer and back to remove leading zeros, then format
            number = int(number_part)
            label_filename = f"FLARE23_{number}.nii.gz"
            
            # CT path relative to data root
            ct_path = f"CT/06_FLARE2023/{filename}"
            # Label path
            label_path = f"label/liver_label/{label_filename}"
            
            lines.append(f"{ct_path}      {label_path}")
    
    # Write to output file
    with open(output_file, 'w') as f:
        for line in lines:
            f.write(line + '\n')
    
    print(f"Generated {len(lines)} entries in {output_file}")
    print(f"AbdomenAtlas entries: {len(abdomen_ct_files)}")
    print(f"FLARE2023 entries: {len(flare_ct_files)}")
    
    # Show first few lines as example
    print("\nFirst 5 lines:")
    for i, line in enumerate(lines[:5]):
        print(f"  {line}")
    
    print("\nLast 5 lines:")
    for i, line in enumerate(lines[-5:]):
        print(f"  {line}")

if __name__ == "__main__":
    main()
