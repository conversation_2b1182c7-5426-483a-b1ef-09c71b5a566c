#!/usr/bin/env python3
"""
脚本用于将label文件的affine矩阵替换为对应image文件的affine矩阵

处理两个数据集：
1. 05_AbdomenAtlas: image和label文件同名
2. 06_FLARE2023: image文件名末尾有"_0000"，label文件名需要去掉这个后缀

使用方法:
python replace_label_affine.py
"""

import os
import glob
import nibabel as nib
import numpy as np
from pathlib import Path
import argparse


def replace_affine(image_path, label_path, output_path=None):
    """
    将label文件的affine矩阵替换为image文件的affine矩阵

    Args:
        image_path: image文件路径
        label_path: label文件路径
        output_path: 输出路径，如果为None则覆盖原label文件
    """
    try:
        # 加载image和label文件
        image_nii = nib.load(image_path)
        label_nii = nib.load(label_path)

        # 获取image的affine矩阵（确保使用完全相同的矩阵）
        image_affine = image_nii.affine.copy()
        label_affine = label_nii.affine

        # 检查是否需要更新（使用更严格的比较）
        if np.allclose(image_affine, label_affine, rtol=1e-10, atol=1e-10):
            print(f"跳过（affine已匹配）: {os.path.basename(label_path)}")
            return

        # 创建新的label文件，确保使用完全相同的affine矩阵
        # 使用label的原始数据类型和header，但强制使用image的affine
        new_label_nii = nib.Nifti1Image(
            label_nii.get_fdata().astype(label_nii.get_data_dtype()),
            affine=image_affine,
            header=label_nii.header.copy()
        )

        # 确保header中的affine信息也更新
        new_label_nii.header.set_sform(image_affine)
        new_label_nii.header.set_qform(image_affine)

        # 保存文件
        if output_path is None:
            output_path = label_path

        nib.save(new_label_nii, output_path)
        print(f"成功处理: {os.path.basename(label_path)}")

    except Exception as e:
        print(f"处理失败 {os.path.basename(label_path)}: {str(e)}")


def process_abdomen_atlas(image_dir, label_dir, dry_run=False):
    """
    处理05_AbdomenAtlas数据集
    image和label文件同名
    """
    print("处理 05_AbdomenAtlas 数据集...")
    
    # 获取所有image文件
    image_files = glob.glob(os.path.join(image_dir, "*.nii.gz"))
    
    processed_count = 0
    for image_path in image_files:
        image_name = os.path.basename(image_path)
        label_path = os.path.join(label_dir, image_name)
        
        if os.path.exists(label_path):
            if dry_run:
                print(f"[DRY RUN] 将处理: {image_name}")
            else:
                replace_affine(image_path, label_path)
            processed_count += 1
        else:
            print(f"警告: 找不到对应的label文件: {image_name}")
    
    print(f"05_AbdomenAtlas: 处理了 {processed_count} 个文件")


def process_flare2023(image_dir, label_dir, dry_run=False):
    """
    处理06_FLARE2023数据集
    image文件名末尾有"_0000"，label文件名需要去掉这个后缀
    """
    print("处理 06_FLARE2023 数据集...")
    
    # 获取所有image文件
    image_files = glob.glob(os.path.join(image_dir, "*_0000.nii.gz"))
    
    processed_count = 0
    for image_path in image_files:
        image_name = os.path.basename(image_path)
        # 去掉"_0000"后缀得到label文件名
        label_name = image_name.replace("_0000.nii.gz", ".nii.gz")
        label_path = os.path.join(label_dir, label_name)
        
        if os.path.exists(label_path):
            if dry_run:
                print(f"[DRY RUN] 将处理: {image_name} -> {label_name}")
            else:
                replace_affine(image_path, label_path)
            processed_count += 1
        else:
            print(f"警告: 找不到对应的label文件: {label_name}")
    
    print(f"06_FLARE2023: 处理了 {processed_count} 个文件")


def main():
    parser = argparse.ArgumentParser(description='替换label文件的affine矩阵为对应image文件的affine矩阵')
    parser.add_argument('--dry-run', action='store_true', help='只显示将要处理的文件，不实际执行')
    parser.add_argument('--data-root', default='/home/<USER>/DiffTumor/data/HealthyCT', 
                       help='数据根目录路径')
    
    args = parser.parse_args()
    
    # 设置路径
    data_root = args.data_root
    
    # 05_AbdomenAtlas路径
    abdomen_image_dir = os.path.join(data_root, "CT", "05_AbdomenAtlas")
    
    # 06_FLARE2023路径
    flare_image_dir = os.path.join(data_root, "CT", "06_FLARE2023")
    
    # label目录
    label_dir = os.path.join(data_root, "label", "liver_label")
    
    # 检查目录是否存在
    for dir_path, name in [(abdomen_image_dir, "05_AbdomenAtlas"), 
                          (flare_image_dir, "06_FLARE2023"), 
                          (label_dir, "liver_label")]:
        if not os.path.exists(dir_path):
            print(f"错误: 目录不存在: {dir_path}")
            return
    
    if args.dry_run:
        print("=== DRY RUN 模式 ===")
    
    # 处理两个数据集
    process_abdomen_atlas(abdomen_image_dir, label_dir, args.dry_run)
    print()
    process_flare2023(flare_image_dir, label_dir, args.dry_run)
    
    if not args.dry_run:
        print("\n所有文件处理完成！")
    else:
        print("\n=== DRY RUN 完成，使用 --dry-run 参数查看将要处理的文件 ===")


if __name__ == "__main__":
    main()
