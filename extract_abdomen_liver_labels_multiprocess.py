#!/usr/bin/env python3
"""
Multi-process script to extract liver labels (label value = 5) from AbdomenAtlasMini dataset labels.
This script processes all label files in the labelsTr directory and creates
binary liver masks where only liver regions (label=5) are preserved and relabeled as 1.
"""

import os
import glob
import nibabel as nib
import numpy as np
from tqdm import tqdm
import argparse
import multiprocessing as mp
from functools import partial
import time

def extract_liver_label_single(input_path, output_dir):
    """
    Extract liver label (value=5) from a multi-label segmentation file and relabel as 1.
    
    Args:
        input_path (str): Path to input label file
        output_dir (str): Directory to save the liver-only label file
        
    Returns:
        tuple: (success, filename, message)
    """
    try:
        filename = os.path.basename(input_path)
        output_path = os.path.join(output_dir, filename)
        
        # Skip if output file already exists
        if os.path.exists(output_path):
            return (True, filename, "Already exists - skipped")
        
        # Load the NIfTI file
        nii_img = nib.load(input_path)
        label_data = nii_img.get_fdata()
        
        # Create liver mask (label=5 becomes 1, everything else becomes 0)
        liver_mask = (label_data == 5).astype(np.uint8)
        
        # Create new NIfTI image with the same header and affine
        liver_nii = nib.Nifti1Image(liver_mask, nii_img.affine, nii_img.header)
        
        # Save the liver mask
        nib.save(liver_nii, output_path)
        
        return (True, filename, "Success")
        
    except Exception as e:
        return (False, filename, f"Error: {str(e)}")

def process_batch(file_batch, output_dir, process_id):
    """
    Process a batch of files in a single process.
    
    Args:
        file_batch (list): List of file paths to process
        output_dir (str): Output directory
        process_id (int): Process ID for logging
        
    Returns:
        tuple: (successful_count, failed_count, results)
    """
    successful = 0
    failed = 0
    results = []
    
    for file_path in file_batch:
        success, filename, message = extract_liver_label_single(file_path, output_dir)
        results.append((success, filename, message))
        
        if success:
            successful += 1
        else:
            failed += 1
    
    return (successful, failed, results)

def main():
    parser = argparse.ArgumentParser(description='Extract liver labels from AbdomenAtlasMini dataset using multiprocessing')
    parser.add_argument('--input_dir', 
                       default='data/Dataset100_AbdomenAtlasMini/labelsTr',
                       help='Input directory containing label files')
    parser.add_argument('--output_dir', 
                       default='data/Dataset100_AbdomenAtlasMini/labelsTr_liver',
                       help='Output directory for liver-only labels')
    parser.add_argument('--num_processes', 
                       type=int, 
                       default=8,
                       help='Number of processes to use (default: 8)')
    
    args = parser.parse_args()
    
    input_dir = args.input_dir
    output_dir = args.output_dir
    num_processes = args.num_processes
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Find all .nii.gz files in the input directory
    label_files = glob.glob(os.path.join(input_dir, "*.nii.gz"))
    
    if not label_files:
        print(f"No .nii.gz files found in {input_dir}")
        return
    
    print(f"Found {len(label_files)} label files to process")
    print(f"Input directory: {input_dir}")
    print(f"Output directory: {output_dir}")
    print(f"Using {num_processes} processes")
    print(f"Extracting liver labels (original label=5 -> new label=1)")
    
    # Split files into batches for each process
    batch_size = len(label_files) // num_processes
    if batch_size == 0:
        batch_size = 1
        num_processes = len(label_files)
    
    file_batches = []
    for i in range(num_processes):
        start_idx = i * batch_size
        if i == num_processes - 1:  # Last process gets remaining files
            end_idx = len(label_files)
        else:
            end_idx = (i + 1) * batch_size
        
        if start_idx < len(label_files):
            file_batches.append(label_files[start_idx:end_idx])
    
    print(f"Split into {len(file_batches)} batches")
    for i, batch in enumerate(file_batches):
        print(f"  Process {i}: {len(batch)} files")
    
    # Start timing
    start_time = time.time()
    
    # Process files using multiprocessing
    with mp.Pool(processes=num_processes) as pool:
        # Create partial function with fixed output_dir
        process_func = partial(process_batch, output_dir=output_dir)
        
        # Map batches to processes with process IDs
        batch_args = [(batch, i) for i, batch in enumerate(file_batches)]
        
        # Process batches
        results = []
        for i, batch in enumerate(file_batches):
            result = pool.apply_async(process_batch, (batch, output_dir, i))
            results.append(result)
        
        # Collect results with progress bar
        total_successful = 0
        total_failed = 0
        all_results = []
        
        print("\nProcessing files...")
        for i, result in enumerate(results):
            successful, failed, batch_results = result.get()
            total_successful += successful
            total_failed += failed
            all_results.extend(batch_results)
            print(f"Process {i} completed: {successful} successful, {failed} failed")
    
    # End timing
    end_time = time.time()
    processing_time = end_time - start_time
    
    print(f"\nProcessing completed in {processing_time:.2f} seconds!")
    print(f"Successfully processed: {total_successful} files")
    print(f"Failed: {total_failed} files")
    
    # Show failed files if any
    if total_failed > 0:
        print(f"\nFailed files:")
        for success, filename, message in all_results:
            if not success:
                print(f"  {filename}: {message}")
    
    # Verify results
    if total_successful > 0:
        print(f"\nOutput files saved to: {output_dir}")
        output_files = glob.glob(os.path.join(output_dir, "*.nii.gz"))
        print(f"Total output files: {len(output_files)}")
        
        if len(output_files) > 0:
            avg_time_per_file = processing_time / total_successful
            print(f"Average processing time per file: {avg_time_per_file:.3f} seconds")
            print(f"Processing speed: {total_successful/processing_time:.2f} files/second")

if __name__ == "__main__":
    main()
