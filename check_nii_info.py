#!/usr/bin/env python3
"""
检查NIfTI文件的shape和spacing信息
"""

import nibabel as nib
import numpy as np

def check_nii_file(file_path):
    """检查单个NIfTI文件的信息"""
    try:
        nii = nib.load(file_path)
        data = nii.get_fdata()
        header = nii.header
        affine = nii.affine
        
        print(f"\n文件: {file_path}")
        print(f"Shape: {data.shape}")
        print(f"Data type: {data.dtype}")
        print(f"Spacing (pixdim): {header.get_zooms()}")
        print(f"Affine matrix:")
        print(affine)
        
        return {
            'shape': data.shape,
            'dtype': data.dtype,
            'spacing': header.get_zooms(),
            'affine': affine
        }
        
    except Exception as e:
        print(f"读取文件失败 {file_path}: {str(e)}")
        return None

def compare_files(image_path, label_path):
    """比较两个文件的信息"""
    print("=" * 80)
    print("检查文件信息")
    print("=" * 80)
    
    image_info = check_nii_file(image_path)
    label_info = check_nii_file(label_path)
    
    if image_info and label_info:
        print("\n" + "=" * 80)
        print("比较结果")
        print("=" * 80)
        
        # 比较shape
        shape_match = image_info['shape'] == label_info['shape']
        print(f"Shape匹配: {shape_match}")
        if not shape_match:
            print(f"  Image shape: {image_info['shape']}")
            print(f"  Label shape: {label_info['shape']}")
        
        # 比较spacing
        spacing_match = np.allclose(image_info['spacing'], label_info['spacing'], rtol=1e-5)
        print(f"Spacing匹配: {spacing_match}")
        if not spacing_match:
            print(f"  Image spacing: {image_info['spacing']}")
            print(f"  Label spacing: {label_info['spacing']}")
        
        # 比较affine
        affine_match = np.allclose(image_info['affine'], label_info['affine'], rtol=1e-5)
        print(f"Affine矩阵匹配: {affine_match}")
        if not affine_match:
            print(f"  Affine差异:")
            diff = image_info['affine'] - label_info['affine']
            print(f"  最大差异: {np.max(np.abs(diff))}")
            print(f"  Image affine:")
            print(f"  {image_info['affine']}")
            print(f"  Label affine:")
            print(f"  {label_info['affine']}")

if __name__ == "__main__":
    image_path = "/home/<USER>/DiffTumor/data/HealthyCT/CT/05_AbdomenAtlas/AbdomenAtlasMini_00002385.nii.gz"
    label_path = "/home/<USER>/DiffTumor/data/HealthyCT/label/liver_label/AbdomenAtlasMini_00002385.nii.gz"
    
    compare_files(image_path, label_path)
