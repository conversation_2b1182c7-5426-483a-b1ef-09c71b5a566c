#!/usr/bin/env python3
"""
Script to check if CT image and corresponding label have consistent shapes.
"""

import nibabel as nib
import numpy as np
import sys

def check_file_shapes(ct_path, label_path):
    """
    Check if CT and label files have consistent shapes.
    
    Args:
        ct_path (str): Path to CT image file
        label_path (str): Path to label file
        
    Returns:
        bool: True if shapes match, False otherwise
    """
    try:
        # Load CT image
        print(f"Loading CT image: {ct_path}")
        ct_img = nib.load(ct_path)
        ct_data = ct_img.get_fdata()
        ct_shape = ct_data.shape
        
        print(f"CT image shape: {ct_shape}")
        print(f"CT image data type: {ct_data.dtype}")
        print(f"CT image affine:\n{ct_img.affine}")
        
        # Load label image
        print(f"\nLoading label image: {label_path}")
        label_img = nib.load(label_path)
        label_data = label_img.get_fdata()
        label_shape = label_data.shape
        
        print(f"Label image shape: {label_shape}")
        print(f"Label image data type: {label_data.dtype}")
        print(f"Label image affine:\n{label_img.affine}")
        
        # Check if shapes match
        shapes_match = ct_shape == label_shape
        print(f"\nShapes match: {shapes_match}")
        
        if not shapes_match:
            print(f"Shape mismatch!")
            print(f"  CT shape:    {ct_shape}")
            print(f"  Label shape: {label_shape}")
            return False
        
        # Check if affine matrices match (with reasonable tolerance)
        affines_match = np.allclose(ct_img.affine, label_img.affine, atol=1e-4)
        print(f"Affine matrices match (tolerance=1e-4): {affines_match}")

        if not affines_match:
            print("Affine matrices differ:")
            diff = ct_img.affine - label_img.affine
            max_diff = np.max(np.abs(diff))
            print(f"Maximum absolute difference: {max_diff:.2e}")
            print(f"Difference matrix:\n{diff}")

            # Check if differences are very small (acceptable for medical imaging)
            if max_diff < 1e-3:
                print("⚠️  Differences are very small and likely acceptable for medical imaging")
                affines_match = True

        # Check label values
        unique_labels = np.unique(label_data)
        print(f"\nUnique label values: {unique_labels}")

        # Check CT value range
        ct_min, ct_max = np.min(ct_data), np.max(ct_data)
        print(f"CT value range: [{ct_min:.2f}, {ct_max:.2f}]")

        # Additional checks
        print(f"\nAdditional information:")
        print(f"CT voxel spacing: {np.diag(ct_img.affine)[:3]}")
        print(f"Label voxel spacing: {np.diag(label_img.affine)[:3]}")

        return shapes_match and affines_match
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

def main():
    ct_path = "/home/<USER>/DiffTumor/data/HealthyCT/CT/05_AbdomenAtlas/AbdomenAtlasMini_00000014.nii.gz"
    label_path = "/home/<USER>/DiffTumor/data/HealthyCT/label/liver_label/AbdomenAtlasMini_00000014.nii.gz"
    
    print("Checking shape consistency between CT and label files")
    print("=" * 60)
    
    result = check_file_shapes(ct_path, label_path)
    
    print("\n" + "=" * 60)
    if result:
        print("✅ Files are consistent!")
    else:
        print("❌ Files have inconsistencies!")
    
    return 0 if result else 1

if __name__ == "__main__":
    sys.exit(main())
