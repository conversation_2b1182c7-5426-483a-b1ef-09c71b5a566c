#!/usr/bin/env python3
"""
强制复制affine矩阵，确保完全相同
"""

import nibabel as nib
import numpy as np
import os

def force_copy_affine(image_path, label_path):
    """强制复制affine矩阵，确保完全相同"""
    try:
        # 加载文件
        print(f"加载image文件: {image_path}")
        image_nii = nib.load(image_path)
        
        print(f"加载label文件: {label_path}")
        label_nii = nib.load(label_path)
        
        # 获取image的affine矩阵
        image_affine = image_nii.affine
        
        print("原始Image affine:")
        print(image_affine)
        print("原始Label affine:")
        print(label_nii.affine)
        
        # 获取label数据
        label_data = label_nii.get_fdata()
        
        # 创建新的header，复制label的header但更新affine相关信息
        new_header = label_nii.header.copy()
        
        # 创建新的NIfTI图像，直接使用image的affine矩阵
        new_label_nii = nib.Nifti1Image(
            label_data.astype(label_nii.get_data_dtype()),
            affine=image_affine.copy(),  # 使用copy确保独立的矩阵
            header=new_header
        )
        
        # 手动设置header中的affine信息
        new_label_nii.header.set_sform(image_affine, code=1)
        new_label_nii.header.set_qform(image_affine, code=1)
        
        # 直接修改内部的affine属性
        new_label_nii._affine = image_affine.copy()
        
        # 保存文件
        print(f"保存修改后的label文件...")
        nib.save(new_label_nii, label_path)
        
        # 重新加载验证
        print("验证结果...")
        verify_nii = nib.load(label_path)
        
        print("修改后的Label affine:")
        print(verify_nii.affine)
        
        # 检查是否完全相同
        exact_match = np.array_equal(image_affine, verify_nii.affine)
        print(f"\n完全相同: {exact_match}")
        
        if not exact_match:
            diff = image_affine - verify_nii.affine
            max_diff = np.max(np.abs(diff))
            print(f"最大差异: {max_diff}")
            
            # 如果仍有差异，尝试直接修改文件
            if max_diff > 0:
                print("尝试直接修改文件的affine矩阵...")
                
                # 重新创建，使用更精确的方法
                final_nii = nib.Nifti1Image(
                    label_data.astype(label_nii.get_data_dtype()),
                    affine=image_affine,
                    header=new_header
                )
                
                # 强制设置affine
                final_nii._affine = image_affine
                
                # 保存
                nib.save(final_nii, label_path)
                
                # 最终验证
                final_verify = nib.load(label_path)
                final_match = np.array_equal(image_affine, final_verify.affine)
                print(f"最终验证 - 完全相同: {final_match}")
                
                if not final_match:
                    final_diff = np.max(np.abs(image_affine - final_verify.affine))
                    print(f"最终最大差异: {final_diff}")
        else:
            print("✓ Affine矩阵复制成功，完全相同！")
            
        return True
        
    except Exception as e:
        print(f"操作失败: {str(e)}")
        return False

if __name__ == "__main__":
    image_path = "/home/<USER>/DiffTumor/data/HealthyCT/CT/05_AbdomenAtlas/AbdomenAtlasMini_00002385.nii.gz"
    label_path = "/home/<USER>/DiffTumor/data/HealthyCT/label/liver_label/AbdomenAtlasMini_00002385.nii.gz"
    
    print("=" * 80)
    print("强制复制Affine矩阵")
    print("=" * 80)
    
    force_copy_affine(image_path, label_path)
