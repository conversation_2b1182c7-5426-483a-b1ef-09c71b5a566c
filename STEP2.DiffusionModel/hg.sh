vqgan_ckpt=pretrained_models/AutoencoderModel.ckpt
fold=0
datapath=/home/<USER>/DiffTumor/data/Dataset017_Liver/
tumorlabel=/home/<USER>/DiffTumor/STEP2.DiffusionModel/preprocessed_labels/

CUDA_VISIBLE_DEVICES=6 python train.py dataset.name=liver_tumor_train dataset.fold=$fold dataset.data_root_path=$datapath dataset.label_root_path=$tumorlabel dataset.dataset_list=['liver_tumor_data_early_fold'] dataset.uniform_sample=False model.results_folder_postfix="liver_early_tumor_fold"$fold""  model.vqgan_ckpt=$vqgan_ckpt

# sbatch --error=logs/diffusion_model.out --output=logs/diffusion_model.out hg.sh
