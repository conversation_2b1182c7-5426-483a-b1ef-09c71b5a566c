vqgan_ckpt: 'AutoencoderModel.ckpt'

# Have to be derived from VQ-GAN Latent space dimensions
diffusion_img_size: 24
diffusion_depth_size: 24
diffusion_num_channels: 17
out_dim: 8
dim_mults: [1,2,4,8]
results_folder: checkpoints/ddpm/
results_folder_postfix: 'fold0_tumor_early_96_t4'
load_milestone: False

batch_size: 2 # 40
num_workers: 20
logger: wandb
objective: pred_x0
save_and_sample_every: 1000
denoising_fn: Unet3D
train_lr: 1e-4
timesteps: 4 # number of steps
sampling_timesteps: 250 # number of sampling timesteps (using ddim for faster inference [see citation for ddim paper])
loss_type: l1 # L1 or L2
train_num_steps: 60000 # total training steps
gradient_accumulate_every: 2 # gradient accumulation steps
ema_decay: 0.995 # exponential moving average decay
amp: False # turn on mixed precision
num_sample_rows: 1
gpus: 0
