hydra:
  run:
    dir: outputs/${now:%Y-%m-%d}/${now:%H-%M-%S}
  sweep:
    dir: multirun/${now:%Y-%m-%d}/${now:%H-%M-%S}
    subdir: ${hydra.job.num}
  launcher:
    _target_: hydra._internal.core_plugins.basic_launcher.BasicLauncher
  sweeper:
    _target_: hydra._internal.core_plugins.basic_sweeper.BasicSweeper
    max_batch_size: null
    params: null
  help:
    app_name: ${hydra.job.name}
    header: '${hydra.help.app_name} is powered by Hydra.

      '
    footer: 'Powered by Hydra (https://hydra.cc)

      Use --hydra-help to view Hydra specific help

      '
    template: '${hydra.help.header}

      == Configuration groups ==

      Compose your configuration from those groups (group=option)


      $APP_CONFIG_GROUPS


      == Config ==

      Override anything in the config (foo.bar=value)


      $CONFIG


      ${hydra.help.footer}

      '
  hydra_help:
    template: 'Hydra (${hydra.runtime.version})

      See https://hydra.cc for more info.


      == Flags ==

      $FLAGS_HELP


      == Configuration groups ==

      Compose your configuration from those groups (For example, append hydra/job_logging=disabled
      to command line)


      $HYDRA_CONFIG_GROUPS


      Use ''--cfg hydra'' to Show the Hydra config.

      '
    hydra_help: ???
  hydra_logging:
    version: 1
    formatters:
      simple:
        format: '[%(asctime)s][HYDRA] %(message)s'
    handlers:
      console:
        class: logging.StreamHandler
        formatter: simple
        stream: ext://sys.stdout
    root:
      level: INFO
      handlers:
      - console
    loggers:
      logging_example:
        level: DEBUG
    disable_existing_loggers: false
  job_logging:
    version: 1
    formatters:
      simple:
        format: '[%(asctime)s][%(name)s][%(levelname)s] - %(message)s'
    handlers:
      console:
        class: logging.StreamHandler
        formatter: simple
        stream: ext://sys.stdout
      file:
        class: logging.FileHandler
        formatter: simple
        filename: ${hydra.runtime.output_dir}/${hydra.job.name}.log
    root:
      level: INFO
      handlers:
      - console
      - file
    disable_existing_loggers: false
  env: {}
  mode: RUN
  searchpath: []
  callbacks: {}
  output_subdir: .hydra
  overrides:
    hydra:
    - hydra.mode=RUN
    task:
    - dataset.name=liver_tumor_train
    - dataset.fold=0
    - dataset.data_root_path=/home/<USER>/DiffTumor/data/Dataset017_Liver/
    - dataset.label_root_path=/home/<USER>/DiffTumor/STEP2.DiffusionModel/preprocessed_labels/
    - dataset.dataset_list=[liver_tumor_data_early_fold]
    - dataset.uniform_sample=False
    - model.results_folder_postfix=liver_early_tumor_fold0
    - model.vqgan_ckpt=pretrained_models/AutoencoderModel.ckpt
  job:
    name: train
    chdir: null
    override_dirname: dataset.data_root_path=/home/<USER>/DiffTumor/data/Dataset017_Liver/,dataset.dataset_list=[liver_tumor_data_early_fold],dataset.fold=0,dataset.label_root_path=/home/<USER>/DiffTumor/STEP2.DiffusionModel/preprocessed_labels/,dataset.name=liver_tumor_train,dataset.uniform_sample=False,model.results_folder_postfix=liver_early_tumor_fold0,model.vqgan_ckpt=pretrained_models/AutoencoderModel.ckpt
    id: ???
    num: ???
    config_name: base_cfg
    env_set: {}
    env_copy: []
    config:
      override_dirname:
        kv_sep: '='
        item_sep: ','
        exclude_keys: []
  runtime:
    version: 1.2.0
    version_base: '1.2'
    cwd: /home/<USER>/DiffTumor/STEP2.DiffusionModel
    config_sources:
    - path: hydra.conf
      schema: pkg
      provider: hydra
    - path: /home/<USER>/DiffTumor/STEP2.DiffusionModel/config
      schema: file
      provider: main
    - path: ''
      schema: structured
      provider: schema
    output_dir: /home/<USER>/DiffTumor/STEP2.DiffusionModel/outputs/2025-08-20/13-03-52
    choices:
      model: ddpm
      dataset: synt_ct
      hydra/env: default
      hydra/callbacks: null
      hydra/job_logging: default
      hydra/hydra_logging: default
      hydra/hydra_help: default
      hydra/help: default
      hydra/sweeper: basic
      hydra/launcher: basic
      hydra/output: default
  verbose: false
