#!/usr/bin/env python3
"""
Multi-process script to extract kidney labels (label values = 2 and 3) from AbdomenAtlasMini dataset labels.
This script processes all label files and creates kidney masks where kidney regions
(original label 2 -> new label 1, original label 3 -> new label 2).
"""

import os
import glob
import nibabel as nib
import numpy as np
from tqdm import tqdm
import argparse
import multiprocessing as mp
from functools import partial
import time

def get_corresponding_label_path(ct_path, label_dir):
    """
    Get corresponding label path for a CT image.
    
    Args:
        ct_path (str): Path to CT image
        label_dir (str): Directory containing label files
        
    Returns:
        str: Path to corresponding label file
    """
    ct_filename = os.path.basename(ct_path)
    # For AbdomenAtlasMini, the label file has the same name
    label_filename = ct_filename
    label_path = os.path.join(label_dir, label_filename)
    return label_path

def extract_kidney_label_single(ct_path, label_dir, output_dir):
    """
    Extract kidney labels (values=2,3) from a multi-label segmentation file.
    Original label 2 -> new label 1, original label 3 -> new label 2.

    Args:
        ct_path (str): Path to input CT file
        label_dir (str): Directory containing original label files
        output_dir (str): Directory to save the kidney-only label file

    Returns:
        tuple: (success, filename, message)
    """
    try:
        ct_filename = os.path.basename(ct_path)
        label_path = get_corresponding_label_path(ct_path, label_dir)
        output_path = os.path.join(output_dir, ct_filename)
        
        # Check if CT file exists
        if not os.path.exists(ct_path):
            return (False, ct_filename, f"CT file not found: {ct_path}")
        
        # Check if label file exists
        if not os.path.exists(label_path):
            return (False, ct_filename, f"Label file not found: {label_path}")
        
        # Skip if output file already exists
        if os.path.exists(output_path):
            return (True, ct_filename, "Already exists - skipped")
        
        # Load the NIfTI label file
        label_img = nib.load(label_path)
        label_data = label_img.get_fdata()

        # Create kidney mask (original label 2 -> new label 1, original label 3 -> new label 2)
        kidney_mask = np.zeros_like(label_data, dtype=np.uint8)
        kidney_mask[label_data == 2] = 1  # Right kidney -> label 1
        kidney_mask[label_data == 3] = 2  # Left kidney -> label 2

        # Check if any kidney voxels were found
        kidney_voxels = np.sum(kidney_mask > 0)
        if kidney_voxels == 0:
            return (True, ct_filename, "No kidney labels found (labels 2,3)")

        # Count each kidney separately
        right_kidney_voxels = np.sum(kidney_mask == 1)
        left_kidney_voxels = np.sum(kidney_mask == 2)
        
        # Load CT image to get the correct affine matrix
        ct_img = nib.load(ct_path)
        
        # Verify shapes match
        if ct_img.shape != label_img.shape:
            return (False, ct_filename, f"Shape mismatch: CT {ct_img.shape} vs Label {label_img.shape}")
        
        # Create new NIfTI image with CT affine matrix
        kidney_nii = nib.Nifti1Image(kidney_mask, ct_img.affine, ct_img.header)
        
        # Save the kidney mask
        nib.save(kidney_nii, output_path)
        
        return (True, ct_filename, f"Success - R:{right_kidney_voxels} L:{left_kidney_voxels} voxels")
        
    except Exception as e:
        return (False, ct_filename, f"Error: {str(e)}")

def process_batch(file_batch, label_dir, output_dir, process_id):
    """
    Process a batch of files in a single process.
    
    Args:
        file_batch (list): List of CT file paths to process
        label_dir (str): Directory containing label files
        output_dir (str): Output directory
        process_id (int): Process ID for logging
        
    Returns:
        tuple: (successful_count, failed_count, results)
    """
    successful = 0
    failed = 0
    results = []
    
    for ct_path in file_batch:
        success, filename, message = extract_kidney_label_single(ct_path, label_dir, output_dir)
        results.append((success, filename, message))
        
        if success:
            successful += 1
        else:
            failed += 1
    
    return (successful, failed, results)

def main():
    parser = argparse.ArgumentParser(description='Extract kidney labels from AbdomenAtlasMini dataset using multiprocessing')
    parser.add_argument('--ct_dir', 
                       default='/home/<USER>/DiffTumor/data/HealthyCT/CT/05_AbdomenAtlas',
                       help='CT directory')
    parser.add_argument('--label_dir', 
                       default='/home/<USER>/DiffTumor/data/Dataset100_AbdomenAtlasMini/labelsTr',
                       help='Original label directory')
    parser.add_argument('--output_dir', 
                       default='/home/<USER>/DiffTumor/data/HealthyCT/label/abdomenatlas_kidney_label',
                       help='Output directory for kidney-only labels')
    parser.add_argument('--num_processes', 
                       type=int, 
                       default=8,
                       help='Number of processes to use (default: 8)')
    
    args = parser.parse_args()
    
    ct_dir = args.ct_dir
    label_dir = args.label_dir
    output_dir = args.output_dir
    num_processes = args.num_processes
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Find all .nii.gz files in the CT directory
    ct_files = glob.glob(os.path.join(ct_dir, "*.nii.gz"))
    ct_files.sort()
    
    if not ct_files:
        print(f"No .nii.gz files found in {ct_dir}")
        return
    
    print(f"Found {len(ct_files)} CT files to process")
    print(f"CT directory: {ct_dir}")
    print(f"Label directory: {label_dir}")
    print(f"Output directory: {output_dir}")
    print(f"Using {num_processes} processes")
    print(f"Extracting kidney labels (original label 2->new label 1, original label 3->new label 2)")
    
    # Check if some label files exist
    sample_ct = ct_files[0]
    sample_label = get_corresponding_label_path(sample_ct, label_dir)
    if not os.path.exists(sample_label):
        print(f"Warning: Sample label file not found: {sample_label}")
        print("Please check the label directory path")
    
    # Split files into batches for each process
    batch_size = len(ct_files) // num_processes
    if batch_size == 0:
        batch_size = 1
        num_processes = len(ct_files)
    
    file_batches = []
    for i in range(num_processes):
        start_idx = i * batch_size
        if i == num_processes - 1:  # Last process gets remaining files
            end_idx = len(ct_files)
        else:
            end_idx = (i + 1) * batch_size
        
        if start_idx < len(ct_files):
            file_batches.append(ct_files[start_idx:end_idx])
    
    print(f"Split into {len(file_batches)} batches")
    for i, batch in enumerate(file_batches):
        print(f"  Process {i}: {len(batch)} files")
    
    # Start timing
    start_time = time.time()
    
    # Process files using multiprocessing
    with mp.Pool(processes=num_processes) as pool:
        # Process batches
        results = []
        for i, batch in enumerate(file_batches):
            result = pool.apply_async(process_batch, (batch, label_dir, output_dir, i))
            results.append(result)
        
        # Collect results with progress bar
        total_successful = 0
        total_failed = 0
        all_results = []
        
        print("\nProcessing files...")
        for i, result in enumerate(results):
            successful, failed, batch_results = result.get()
            total_successful += successful
            total_failed += failed
            all_results.extend(batch_results)
            print(f"Process {i} completed: {successful} successful, {failed} failed")
    
    # End timing
    end_time = time.time()
    processing_time = end_time - start_time
    
    print(f"\nProcessing completed in {processing_time:.2f} seconds!")
    print(f"Successfully processed: {total_successful} files")
    print(f"Failed: {total_failed} files")
    
    # Show failed files if any
    if total_failed > 0:
        print(f"\nFailed files:")
        for success, filename, message in all_results:
            if not success:
                print(f"  {filename}: {message}")
    
    # Show some successful examples
    successful_examples = [(filename, message) for success, filename, message in all_results if success and "Success" in message]
    if successful_examples:
        print(f"\nSample successful files:")
        for filename, message in successful_examples[:5]:
            print(f"  {filename}: {message}")
    
    # Verify results
    if total_successful > 0:
        print(f"\nOutput files saved to: {output_dir}")
        output_files = glob.glob(os.path.join(output_dir, "*.nii.gz"))
        print(f"Total output files: {len(output_files)}")
        
        if len(output_files) > 0:
            avg_time_per_file = processing_time / total_successful
            print(f"Average processing time per file: {avg_time_per_file:.3f} seconds")
            print(f"Processing speed: {total_successful/processing_time:.2f} files/second")

if __name__ == "__main__":
    main()
