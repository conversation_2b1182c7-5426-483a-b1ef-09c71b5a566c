#!/usr/bin/env python3
"""
根据kidney_label_mine目录下的内容生成healthy_kidney_mine.txt文件
"""

import os
import glob

def main():
    # 定义路径
    label_dir = "/home/<USER>/DiffTumor/data/HealthyCT/label/kidney_label_mine"
    output_file = "/home/<USER>/DiffTumor/STEP3.SegmentationModel/cross_eval/liver_aug_data_fold/healthy_kidney_mine.txt"
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    # 获取所有标签文件
    label_files = glob.glob(os.path.join(label_dir, "*.nii.gz"))
    label_files.sort()
    
    print(f"Found {len(label_files)} label files")
    
    # 生成文件内容
    lines = []
    
    for label_file in label_files:
        filename = os.path.basename(label_file)
        
        if filename.startswith("AbdomenAtlasMini_"):
            # AbdomenAtlas文件格式
            ct_path = f"CT/05_AbdomenAtlas/{filename}"
            label_path = f"label/kidney_label_mine/{filename}"
            
        elif filename.startswith("FLARE23_"):
            # FLARE23文件格式
            # 从 FLARE23_0001_0000.nii.gz 提取数字部分
            parts = filename.split("_")
            if len(parts) >= 2:
                number = parts[1]  # 例如 "0001"
                ct_filename = f"FLARE23_{number}_0000.nii.gz"
                ct_path = f"CT/06_FLARE2023/{ct_filename}"
                label_path = f"label/kidney_label_mine/{filename}"
            else:
                print(f"Warning: Unexpected FLARE23 filename format: {filename}")
                continue
        else:
            print(f"Warning: Unknown filename format: {filename}")
            continue
        
        lines.append(f"{ct_path}\t{label_path}")
    
    # 写入文件
    with open(output_file, 'w') as f:
        for line in lines:
            f.write(line + '\n')
    
    print(f"Generated {output_file} with {len(lines)} entries")
    print(f"First few entries:")
    for i, line in enumerate(lines[:5]):
        print(f"  {line}")
    
    if len(lines) > 5:
        print(f"  ...")
        print(f"Last few entries:")
        for line in lines[-3:]:
            print(f"  {line}")

if __name__ == "__main__":
    main()
