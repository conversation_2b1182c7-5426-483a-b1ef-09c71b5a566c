#!/usr/bin/env python3
"""
检查两个文件的spacing信息
"""

import nibabel as nib
import numpy as np

def check_spacing(image_path, label_path):
    """检查两个文件的spacing信息"""
    try:
        # 加载文件
        print(f"加载image文件: {image_path}")
        image_nii = nib.load(image_path)
        
        print(f"加载label文件: {label_path}")
        label_nii = nib.load(label_path)
        
        # 获取spacing信息
        image_spacing = image_nii.header.get_zooms()
        label_spacing = label_nii.header.get_zooms()
        
        print("\n" + "=" * 60)
        print("SPACING 信息")
        print("=" * 60)
        
        print(f"Image spacing: {image_spacing}")
        print(f"Label spacing: {label_spacing}")
        
        # 检查是否相同
        spacing_equal = np.array_equal(image_spacing, label_spacing)
        print(f"\nSpacing完全相同: {spacing_equal}")
        
        if not spacing_equal:
            # 计算差异
            diff = np.array(image_spacing) - np.array(label_spacing)
            max_diff = np.max(np.abs(diff))
            print(f"最大差异: {max_diff}")
            print(f"差异: {diff}")
            
            # 检查在不同容差下是否相同
            tolerances = [1e-10, 1e-8, 1e-6, 1e-4]
            for tol in tolerances:
                close = np.allclose(image_spacing, label_spacing, rtol=tol, atol=tol)
                print(f"容差 {tol}: {close}")
        
        print("\n" + "=" * 60)
        print("HEADER 详细信息")
        print("=" * 60)
        
        print("\nImage header pixdim:")
        print(image_nii.header['pixdim'])
        print("\nLabel header pixdim:")
        print(label_nii.header['pixdim'])
        
        print("\nImage header shape:")
        print(image_nii.header.get_data_shape())
        print("\nLabel header shape:")
        print(label_nii.header.get_data_shape())
        
        # 检查实际数据shape
        print("\nImage data shape:")
        print(image_nii.get_fdata().shape)
        print("\nLabel data shape:")
        print(label_nii.get_fdata().shape)
        
        return spacing_equal
        
    except Exception as e:
        print(f"检查失败: {str(e)}")
        return False

if __name__ == "__main__":
    image_path = "/home/<USER>/DiffTumor/data/HealthyCT/CT/05_AbdomenAtlas/AbdomenAtlasMini_00002385.nii.gz"
    label_path = "/home/<USER>/DiffTumor/data/HealthyCT/label/liver_label/AbdomenAtlasMini_00002385.nii.gz"
    
    check_spacing(image_path, label_path)
