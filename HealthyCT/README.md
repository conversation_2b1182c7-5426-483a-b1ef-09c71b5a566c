---
license: apache-2.0
---
# Dataset Summary

Healthy CT data for abdominal organs (liver, pancreas and kidney) are filtered out from public dataset.

# Downloading Instructions

#### 1- Install the Hugging Face library:
```bash
pip install -U "huggingface_hub[cli]"
```
#### 2- Download the dataset:
```bash
mkdir HealthyCT
cd HealthyCT
huggingface-cli download qicq1c/HealthyCT  --repo-type dataset --local-dir .  --cache-dir ./cache
```

<details>
<summary style="margin-left: 25px;">[Optional] Resume downloading</summary>
<div style="margin-left: 25px;">

In case you had a previous interrupted download, resume it by adding “--resume-download” to the download command:
```bash
huggingface-cli download qicq1c/HealthyCT  --repo-type dataset --local-dir .  --cache-dir ./cache --resume-download
```

</div>
</details>

#### 3- Extract files: the dataset is formatted as a multi-part zip file. Use the first part (AbdomenAtlasMini.zip.001) to extract the dataset. With the GUI, 
just right-click on the file and select "Extract Here". With the command-line, we suggest using 7zip (faster, see below). 
If it is not available, you can concatenate the files and unzip (slower):
```bash
cat healthy_ct.zip* > HealthyCT.zip
unzip -o -q HealthyCT.zip
```

<details>
<summary style="margin-left: 25px;">[Optional] 7zip</summary>
<div style="margin-left: 25px;">
With 7zip, just run:

```bash
7z x healthy_ct.zip.001
```

Installing 7zip (optional):

For Debian/Ubuntu: 
```bash
sudo apt update
sudo apt install p7zip-full
```
For Fedora:
```bash
sudo dnf install p7zip
```
For CentOS/RHEL:
```bash
sudo yum install p7zip
```

</div>
</details>

---

# Uploading Instructions

#### 0- Install 7zip

For Debian/Ubuntu: 
```bash
sudo apt update
sudo apt install p7zip-full
```
<details>
<summary style="margin-left: 25px;">[Optional] Other operating systems</summary>
<div style="margin-left: 25px;">
  
For Fedora:
```bash
sudo dnf install p7zip
```

For CentOS/RHEL:
```bash
sudo yum install p7zip
```

For MacOS:
```bash
brew update
brew install p7zip
```

If you do not have brew installed, another possible solution is:
```bash
sudo port install p7zip
```

</div>
</details>

#### 1- Compress the folder for uploading

To compress the folder you want to upload into multi-part zip file with parts of 5GB (-v5g), take "AbdomenAtlas1.0" as an example:

Note: if AbdomenAtlas1.0ZipFolder and AbdomenAtlas1.0 share the same parent directory, please use the relative path for AbdomenAtlas1.0ZipFolder. Otherwise, please use the absolute path for AbdomenAtlas1.0ZipFolder.

```bash
mkdir /path/to/the/HealthyCT_ZipFolder
cd /path/to/the/HealthyCT
cd ..
7z -v5g a /path/to/the/HealthyCT_ZipFolder/healthy_ct.zip HealthyCT/
```

After zipping, the structure of the compressed folder (e.g., AbdomenAtlas1.0ZipFolder) will be:
```
HealthyCT_ZipFolder
      ├── healthy_ct.zip.001
      ├── healthy_ct.zip.002
      ├── healthy_ct.zip.003
      ├── ...
```

#### 2- Upload to Hugging Face

Create a repository at Hugging Face (e.g., a repository named AbdomenAtlas1.0)

Install huggingface_hub from the PyPi registry
```bash
pip install --upgrade huggingface_hub
```
Upload the compressed folder (e.g., AbdomenAtlas1.0ZipFolder) to this repository
```bash
python uploading_files_to_hugging_face.py --folder_directory /path/to/the/HealthyCT_ZipFolder --repository qicq1c/HealthyCT --repository_type dataset
```


