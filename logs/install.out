
=====================================================================
This module is intended solely for building or source activating user
python environments, i.e.,

    mamba create -n myenv -c conda-forge

or

    source activate myenv

To list available environments, run:

    mamba info --envs

See our docs: https://links.asu.edu/solpy

Any other use is NOT TESTED.
=====================================================================

  
Looking in indexes: https://pypi.org/simple, https://download.pytorch.org/whl/cu113
Collecting torch==1.12.1+cu113
  Using cached https://download.pytorch.org/whl/cu113/torch-1.12.1%2Bcu113-cp38-cp38-linux_x86_64.whl (1837.7 MB)
Collecting torchvision==0.13.1+cu113
  Using cached https://download.pytorch.org/whl/cu113/torchvision-0.13.1%2Bcu113-cp38-cp38-linux_x86_64.whl (23.4 MB)
Collecting torchaudio==0.12.1
  Using cached https://download.pytorch.org/whl/cu113/torchaudio-0.12.1%2Bcu113-cp38-cp38-linux_x86_64.whl (3.8 MB)
Collecting typing-extensions (from torch==1.12.1+cu113)
  Downloading typing_extensions-4.10.0-py3-none-any.whl.metadata (3.0 kB)
Collecting numpy (from torchvision==0.13.1+cu113)
  Using cached numpy-1.24.4-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (5.6 kB)
Collecting requests (from torchvision==0.13.1+cu113)
  Using cached requests-2.31.0-py3-none-any.whl.metadata (4.6 kB)
Collecting pillow!=8.3.*,>=5.3.0 (from torchvision==0.13.1+cu113)
  Downloading https://download.pytorch.org/whl/pillow-10.2.0-cp38-cp38-manylinux_2_28_x86_64.whl (4.5 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 4.5/4.5 MB 49.3 MB/s eta 0:00:00
Collecting charset-normalizer<4,>=2 (from requests->torchvision==0.13.1+cu113)
  Downloading charset_normalizer-3.3.2-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (33 kB)
Collecting idna<4,>=2.5 (from requests->torchvision==0.13.1+cu113)
  Using cached idna-3.6-py3-none-any.whl.metadata (9.9 kB)
Collecting urllib3<3,>=1.21.1 (from requests->torchvision==0.13.1+cu113)
  Downloading urllib3-2.2.1-py3-none-any.whl.metadata (6.4 kB)
Collecting certifi>=2017.4.17 (from requests->torchvision==0.13.1+cu113)
  Downloading certifi-2024.2.2-py3-none-any.whl.metadata (2.2 kB)
Using cached numpy-1.24.4-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (17.3 MB)
Using cached requests-2.31.0-py3-none-any.whl (62 kB)
Downloading typing_extensions-4.10.0-py3-none-any.whl (33 kB)
Downloading certifi-2024.2.2-py3-none-any.whl (163 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 163.8/163.8 kB 4.7 MB/s eta 0:00:00
Downloading charset_normalizer-3.3.2-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (141 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 141.1/141.1 kB 8.2 MB/s eta 0:00:00
Using cached idna-3.6-py3-none-any.whl (61 kB)
Downloading urllib3-2.2.1-py3-none-any.whl (121 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 121.1/121.1 kB 8.1 MB/s eta 0:00:00
Installing collected packages: urllib3, typing-extensions, pillow, numpy, idna, charset-normalizer, certifi, torch, requests, torchvision, torchaudio
Successfully installed certifi-2024.2.2 charset-normalizer-3.3.2 idna-3.6 numpy-1.24.4 pillow-10.2.0 requests-2.31.0 torch-1.12.1+cu113 torchaudio-0.12.1+cu113 torchvision-0.13.1+cu113 typing-extensions-4.10.0 urllib3-2.2.1
Collecting absl-py==1.1.0 (from -r requirements.txt (line 1))
  Downloading absl_py-1.1.0-py3-none-any.whl.metadata (2.3 kB)
Collecting accelerate==0.11.0 (from -r requirements.txt (line 2))
  Downloading accelerate-0.11.0-py3-none-any.whl.metadata (14 kB)
Collecting aiohttp==3.8.1 (from -r requirements.txt (line 3))
  Downloading aiohttp-3.8.1-cp38-cp38-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_12_x86_64.manylinux2010_x86_64.whl.metadata (7.3 kB)
Collecting aiosignal==1.2.0 (from -r requirements.txt (line 4))
  Downloading aiosignal-1.2.0-py3-none-any.whl.metadata (5.5 kB)
Collecting antlr4-python3-runtime==4.9.3 (from -r requirements.txt (line 5))
  Using cached antlr4_python3_runtime-4.9.3-py3-none-any.whl
Collecting async-timeout==4.0.2 (from -r requirements.txt (line 6))
  Downloading async_timeout-4.0.2-py3-none-any.whl.metadata (4.2 kB)
Collecting attrs==21.4.0 (from -r requirements.txt (line 7))
  Downloading attrs-21.4.0-py2.py3-none-any.whl.metadata (9.8 kB)
Collecting autopep8==1.6.0 (from -r requirements.txt (line 8))
  Downloading autopep8-1.6.0-py2.py3-none-any.whl.metadata (16 kB)
Collecting cachetools==5.2.0 (from -r requirements.txt (line 9))
  Downloading cachetools-5.2.0-py3-none-any.whl.metadata (5.1 kB)
Collecting certifi==2022.6.15 (from -r requirements.txt (line 10))
  Downloading certifi-2022.6.15-py3-none-any.whl.metadata (2.8 kB)
Collecting charset-normalizer==2.0.12 (from -r requirements.txt (line 11))
  Downloading charset_normalizer-2.0.12-py3-none-any.whl.metadata (11 kB)
Collecting click==8.1.3 (from -r requirements.txt (line 12))
  Downloading click-8.1.3-py3-none-any.whl.metadata (3.2 kB)
Collecting cycler==0.11.0 (from -r requirements.txt (line 13))
  Downloading cycler-0.11.0-py3-none-any.whl.metadata (785 bytes)
Collecting Deprecated==1.2.13 (from -r requirements.txt (line 14))
  Downloading Deprecated-1.2.13-py2.py3-none-any.whl.metadata (5.8 kB)
Collecting docker-pycreds==0.4.0 (from -r requirements.txt (line 15))
  Downloading docker_pycreds-0.4.0-py2.py3-none-any.whl.metadata (1.8 kB)
Collecting einops==0.4.1 (from -r requirements.txt (line 16))
  Downloading einops-0.4.1-py3-none-any.whl.metadata (10 kB)
Collecting einops-exts==0.0.3 (from -r requirements.txt (line 17))
  Downloading einops_exts-0.0.3-py3-none-any.whl.metadata (608 bytes)
Collecting ema-pytorch==0.0.8 (from -r requirements.txt (line 18))
  Downloading ema_pytorch-0.0.8-py3-none-any.whl.metadata (693 bytes)
Collecting fonttools==4.34.4 (from -r requirements.txt (line 19))
  Downloading fonttools-4.34.4-py3-none-any.whl.metadata (129 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 129.2/129.2 kB 3.0 MB/s eta 0:00:00
Collecting frozenlist==1.3.0 (from -r requirements.txt (line 20))
  Downloading frozenlist-1.3.0-cp38-cp38-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.7 kB)
Collecting fsspec==2022.5.0 (from -r requirements.txt (line 21))
  Downloading fsspec-2022.5.0-py3-none-any.whl.metadata (5.5 kB)
Collecting ftfy==6.1.1 (from -r requirements.txt (line 22))
  Downloading ftfy-6.1.1-py3-none-any.whl.metadata (6.1 kB)
Collecting future==0.18.2 (from -r requirements.txt (line 23))
  Downloading future-0.18.2.tar.gz (829 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 829.2/829.2 kB 16.9 MB/s eta 0:00:00
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Collecting gitdb==4.0.9 (from -r requirements.txt (line 24))
  Downloading gitdb-4.0.9-py3-none-any.whl.metadata (998 bytes)
Collecting GitPython==3.1.27 (from -r requirements.txt (line 25))
  Downloading GitPython-3.1.27-py3-none-any.whl.metadata (1.3 kB)
Collecting google-auth==2.9.0 (from -r requirements.txt (line 26))
  Downloading google_auth-2.9.0-py2.py3-none-any.whl.metadata (3.8 kB)
Collecting google-auth-oauthlib==0.4.6 (from -r requirements.txt (line 27))
  Downloading google_auth_oauthlib-0.4.6-py2.py3-none-any.whl.metadata (2.7 kB)
Collecting grpcio==1.47.0 (from -r requirements.txt (line 28))
  Downloading grpcio-1.47.0-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.0 kB)
Collecting h5py==3.7.0 (from -r requirements.txt (line 29))
  Downloading h5py-3.7.0-cp38-cp38-manylinux_2_12_x86_64.manylinux2010_x86_64.whl.metadata (1.8 kB)
Collecting humanize==4.2.2 (from -r requirements.txt (line 30))
  Downloading humanize-4.2.2-py3-none-any.whl.metadata (7.7 kB)
Collecting hydra-core==1.2.0 (from -r requirements.txt (line 31))
  Downloading hydra_core-1.2.0-py3-none-any.whl.metadata (4.0 kB)
Collecting idna==3.3 (from -r requirements.txt (line 32))
  Downloading idna-3.3-py3-none-any.whl.metadata (9.8 kB)
Collecting imageio==2.19.3 (from -r requirements.txt (line 33))
  Downloading imageio-2.19.3-py3-none-any.whl.metadata (4.9 kB)
Collecting imageio-ffmpeg==0.4.7 (from -r requirements.txt (line 34))
  Downloading imageio_ffmpeg-0.4.7-py3-none-manylinux2010_x86_64.whl.metadata (1.6 kB)
Collecting importlib-metadata==4.12.0 (from -r requirements.txt (line 35))
  Downloading importlib_metadata-4.12.0-py3-none-any.whl.metadata (4.0 kB)
Collecting importlib-resources==5.9.0 (from -r requirements.txt (line 36))
  Downloading importlib_resources-5.9.0-py3-none-any.whl.metadata (4.0 kB)
Collecting joblib==1.1.0 (from -r requirements.txt (line 37))
  Downloading joblib-1.1.0-py2.py3-none-any.whl.metadata (5.2 kB)
Collecting kiwisolver==1.4.3 (from -r requirements.txt (line 38))
  Downloading kiwisolver-1.4.3-cp38-cp38-manylinux_2_5_x86_64.manylinux1_x86_64.whl.metadata (6.3 kB)
Collecting lxml==4.9.1 (from -r requirements.txt (line 39))
  Downloading lxml-4.9.1-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_24_x86_64.whl.metadata (3.3 kB)
Collecting Markdown==3.3.7 (from -r requirements.txt (line 40))
  Downloading Markdown-3.3.7-py3-none-any.whl.metadata (4.6 kB)
Collecting matplotlib==3.5.2 (from -r requirements.txt (line 41))
  Downloading matplotlib-3.5.2-cp38-cp38-manylinux_2_5_x86_64.manylinux1_x86_64.whl.metadata (6.7 kB)
Collecting multidict==6.0.2 (from -r requirements.txt (line 42))
  Downloading multidict-6.0.2-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.1 kB)
Collecting networkx==2.8.5 (from -r requirements.txt (line 43))
  Downloading networkx-2.8.5-py3-none-any.whl.metadata (5.0 kB)
Collecting nibabel==4.0.1 (from -r requirements.txt (line 44))
  Downloading nibabel-4.0.1-py3-none-any.whl.metadata (6.1 kB)
Collecting nilearn==0.9.1 (from -r requirements.txt (line 45))
  Downloading nilearn-0.9.1-py3-none-any.whl.metadata (6.5 kB)
Collecting numpy==1.23.0 (from -r requirements.txt (line 46))
  Downloading numpy-1.23.0-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (2.2 kB)
Collecting oauthlib==3.2.0 (from -r requirements.txt (line 47))
  Downloading oauthlib-3.2.0-py3-none-any.whl.metadata (7.4 kB)
Collecting omegaconf==2.2.3 (from -r requirements.txt (line 48))
  Downloading omegaconf-2.2.3-py3-none-any.whl.metadata (3.9 kB)
Collecting pandas==1.4.3 (from -r requirements.txt (line 49))
  Downloading pandas-1.4.3-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (12 kB)
Collecting Pillow==9.1.1 (from -r requirements.txt (line 50))
  Downloading Pillow-9.1.1-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (8.7 kB)
Collecting pyasn1==0.4.8 (from -r requirements.txt (line 51))
  Downloading pyasn1-0.4.8-py2.py3-none-any.whl.metadata (1.5 kB)
Collecting pyasn1-modules==0.2.8 (from -r requirements.txt (line 52))
  Downloading pyasn1_modules-0.2.8-py2.py3-none-any.whl.metadata (1.9 kB)
Collecting pycodestyle==2.8.0 (from -r requirements.txt (line 53))
  Downloading pycodestyle-2.8.0-py2.py3-none-any.whl.metadata (31 kB)
Collecting pyDeprecate==0.3.1 (from -r requirements.txt (line 54))
  Downloading pyDeprecate-0.3.1-py3-none-any.whl.metadata (10 kB)
Collecting pydicom==2.3.0 (from -r requirements.txt (line 55))
  Downloading pydicom-2.3.0-py3-none-any.whl.metadata (7.1 kB)
Collecting pytorch-lightning==1.6.4 (from -r requirements.txt (line 56))
  Downloading pytorch_lightning-1.6.4-py3-none-any.whl.metadata (32 kB)
Collecting pytz==2022.1 (from -r requirements.txt (line 57))
  Downloading pytz-2022.1-py2.py3-none-any.whl.metadata (21 kB)
Collecting PyWavelets==1.3.0 (from -r requirements.txt (line 58))
  Downloading PyWavelets-1.3.0-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (1.9 kB)
Collecting PyYAML==6.0 (from -r requirements.txt (line 59))
  Downloading PyYAML-6.0-cp38-cp38-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_12_x86_64.manylinux2010_x86_64.whl.metadata (2.0 kB)
Collecting pyzmq==19.0.2 (from -r requirements.txt (line 60))
  Downloading pyzmq-19.0.2-cp38-cp38-manylinux1_x86_64.whl.metadata (5.0 kB)
Collecting regex==2022.6.2 (from -r requirements.txt (line 61))
  Downloading regex-2022.6.2-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (39 kB)
Collecting requests==2.28.0 (from -r requirements.txt (line 62))
  Downloading requests-2.28.0-py3-none-any.whl.metadata (4.6 kB)
Collecting requests-oauthlib==1.3.1 (from -r requirements.txt (line 63))
  Downloading requests_oauthlib-1.3.1-py2.py3-none-any.whl.metadata (10 kB)
Collecting rotary-embedding-torch==0.1.5 (from -r requirements.txt (line 64))
  Downloading rotary_embedding_torch-0.1.5-py3-none-any.whl.metadata (669 bytes)
Collecting rsa==4.8 (from -r requirements.txt (line 65))
  Downloading rsa-4.8-py3-none-any.whl.metadata (3.1 kB)
Collecting scikit-image==0.19.3 (from -r requirements.txt (line 66))
  Downloading scikit_image-0.19.3-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (8.0 kB)
Collecting scikit-learn==1.1.2 (from -r requirements.txt (line 67))
  Downloading scikit_learn-1.1.2-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (10 kB)
Collecting scikit-video==1.1.11 (from -r requirements.txt (line 68))
  Downloading scikit_video-1.1.11-py2.py3-none-any.whl.metadata (1.1 kB)
Collecting scipy==1.8.1 (from -r requirements.txt (line 69))
  Downloading scipy-1.8.1-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (2.2 kB)
Collecting seaborn==0.11.2 (from -r requirements.txt (line 70))
  Downloading seaborn-0.11.2-py3-none-any.whl.metadata (2.3 kB)
Collecting sentry-sdk==1.7.2 (from -r requirements.txt (line 71))
  Downloading sentry_sdk-1.7.2-py2.py3-none-any.whl.metadata (7.7 kB)
Collecting setproctitle==1.2.3 (from -r requirements.txt (line 72))
  Downloading setproctitle-1.2.3-cp38-cp38-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (8.9 kB)
Collecting shortuuid==1.0.9 (from -r requirements.txt (line 73))
  Downloading shortuuid-1.0.9-py3-none-any.whl.metadata (5.7 kB)
Collecting SimpleITK==******* (from -r requirements.txt (line 74))
  Downloading SimpleITK-*******-cp38-cp38-manylinux_2_12_x86_64.manylinux2010_x86_64.whl.metadata (8.1 kB)
Collecting smmap==5.0.0 (from -r requirements.txt (line 75))
  Downloading smmap-5.0.0-py3-none-any.whl.metadata (4.2 kB)
Collecting tensorboard==2.9.1 (from -r requirements.txt (line 76))
  Downloading tensorboard-2.9.1-py3-none-any.whl.metadata (1.9 kB)
Collecting tensorboard-data-server==0.6.1 (from -r requirements.txt (line 77))
  Downloading tensorboard_data_server-0.6.1-py3-none-manylinux2010_x86_64.whl.metadata (1.1 kB)
Collecting tensorboard-plugin-wit==1.8.1 (from -r requirements.txt (line 78))
  Downloading tensorboard_plugin_wit-1.8.1-py3-none-any.whl.metadata (873 bytes)
Collecting threadpoolctl==3.1.0 (from -r requirements.txt (line 79))
  Downloading threadpoolctl-3.1.0-py3-none-any.whl.metadata (9.2 kB)
Collecting tifffile==2022.8.3 (from -r requirements.txt (line 80))
  Downloading tifffile-2022.8.3-py3-none-any.whl.metadata (26 kB)
Collecting toml==0.10.2 (from -r requirements.txt (line 81))
  Downloading toml-0.10.2-py2.py3-none-any.whl.metadata (7.1 kB)
Collecting torch-tb-profiler==0.4.0 (from -r requirements.txt (line 82))
  Downloading torch_tb_profiler-0.4.0-py3-none-any.whl.metadata (1.3 kB)
Collecting torchio==0.18.80 (from -r requirements.txt (line 83))
  Downloading torchio-0.18.80-py2.py3-none-any.whl.metadata (38 kB)
Collecting torchmetrics==0.9.1 (from -r requirements.txt (line 84))
  Downloading torchmetrics-0.9.1-py3-none-any.whl.metadata (20 kB)
Collecting tqdm==4.64.0 (from -r requirements.txt (line 85))
  Downloading tqdm-4.64.0-py2.py3-none-any.whl.metadata (57 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 57.3/57.3 kB 3.6 MB/s eta 0:00:00
Collecting typing_extensions==4.2.0 (from -r requirements.txt (line 86))
  Downloading typing_extensions-4.2.0-py3-none-any.whl.metadata (6.2 kB)
Collecting urllib3==1.26.9 (from -r requirements.txt (line 87))
  Downloading urllib3-1.26.9-py2.py3-none-any.whl.metadata (46 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 46.3/46.3 kB 3.0 MB/s eta 0:00:00
Collecting wandb==0.12.21 (from -r requirements.txt (line 88))
  Downloading wandb-0.12.21-py2.py3-none-any.whl.metadata (7.2 kB)
Collecting Werkzeug==2.1.2 (from -r requirements.txt (line 89))
  Downloading Werkzeug-2.1.2-py3-none-any.whl.metadata (4.4 kB)
Collecting wrapt==1.14.1 (from -r requirements.txt (line 90))
  Downloading wrapt-1.14.1-cp38-cp38-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.7 kB)
Collecting yarl==1.7.2 (from -r requirements.txt (line 91))
  Downloading yarl-1.7.2-cp38-cp38-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_12_x86_64.manylinux2010_x86_64.whl.metadata (19 kB)
Collecting zipp==3.8.0 (from -r requirements.txt (line 92))
  Downloading zipp-3.8.0-py3-none-any.whl.metadata (2.7 kB)
Collecting tensorboardX==2.4.1 (from -r requirements.txt (line 93))
  Downloading tensorboardX-2.4.1-py2.py3-none-any.whl.metadata (5.1 kB)
Collecting protobuf==3.20.1 (from -r requirements.txt (line 94))
  Downloading protobuf-3.20.1-cp38-cp38-manylinux_2_5_x86_64.manylinux1_x86_64.whl.metadata (698 bytes)
Collecting sk-video (from -r requirements.txt (line 95))
  Downloading sk_video-1.1.10-py2.py3-none-any.whl.metadata (1.0 kB)
Collecting torchstat (from -r requirements.txt (line 96))
  Downloading torchstat-0.0.7-py3-none-any.whl.metadata (4.1 kB)
Collecting timm (from -r requirements.txt (line 97))
  Downloading timm-0.9.16-py3-none-any.whl.metadata (38 kB)
Collecting elasticdeform (from -r requirements.txt (line 98))
  Downloading elasticdeform-0.5.0-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (9.6 kB)
Collecting opencv-python (from -r requirements.txt (line 99))
  Downloading opencv_python-********-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (20 kB)
Collecting monai==0.9.0 (from -r requirements.txt (line 101))
  Downloading monai-0.9.0-202206131636-py3-none-any.whl.metadata (7.3 kB)
Collecting ml-collections (from -r requirements.txt (line 103))
  Using cached ml_collections-0.1.1-py3-none-any.whl
Collecting glob2 (from -r requirements.txt (line 104))
  Using cached glob2-0.7-py2.py3-none-any.whl
Collecting packaging>=20.0 (from accelerate==0.11.0->-r requirements.txt (line 2))
  Using cached packaging-23.2-py3-none-any.whl.metadata (3.2 kB)
Collecting psutil (from accelerate==0.11.0->-r requirements.txt (line 2))
  Downloading psutil-5.9.8-cp36-abi3-manylinux_2_12_x86_64.manylinux2010_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (21 kB)
Requirement already satisfied: torch>=1.4.0 in /home/<USER>/.conda/envs/difftumor/lib/python3.8/site-packages (from accelerate==0.11.0->-r requirements.txt (line 2)) (1.12.1+cu113)
Collecting six>=1.4.0 (from docker-pycreds==0.4.0->-r requirements.txt (line 15))
  Downloading six-1.16.0-py2.py3-none-any.whl.metadata (1.8 kB)
Collecting wcwidth>=0.2.5 (from ftfy==6.1.1->-r requirements.txt (line 22))
  Downloading wcwidth-0.2.13-py2.py3-none-any.whl.metadata (14 kB)
Collecting pyparsing>=2.2.1 (from matplotlib==3.5.2->-r requirements.txt (line 41))
  Using cached pyparsing-3.1.1-py3-none-any.whl.metadata (5.1 kB)
Collecting python-dateutil>=2.7 (from matplotlib==3.5.2->-r requirements.txt (line 41))
  Downloading python_dateutil-2.9.0.post0-py2.py3-none-any.whl.metadata (8.4 kB)
Requirement already satisfied: setuptools in /home/<USER>/.conda/envs/difftumor/lib/python3.8/site-packages (from nibabel==4.0.1->-r requirements.txt (line 44)) (68.2.2)
INFO: pip is looking at multiple versions of tensorboard to determine which version is compatible with other requirements. This could take a while.
ERROR: Cannot install -r requirements.txt (line 56), -r requirements.txt (line 76) and protobuf==3.20.1 because these package versions have conflicting dependencies.

The conflict is caused by:
    The user requested protobuf==3.20.1
    pytorch-lightning 1.6.4 depends on protobuf<=3.20.1
    tensorboard 2.9.1 depends on protobuf<3.20 and >=3.9.2

To fix this you could try to:
1. loosen the range of package versions you've specified
2. remove package versions to allow pip attempt to solve the dependency conflict

ERROR: ResolutionImpossible: for help visit https://pip.pypa.io/en/latest/topics/dependency-resolution/#dealing-with-dependency-conflicts
