#!/usr/bin/env python3
"""
<PERSON>ript to fix label affine matrices by replacing them with corresponding CT image affine matrices.
This script processes AbdomenAtlas (05) and FLARE2023 (06) datasets.
"""

import os
import glob
import nibabel as nib
import numpy as np
from tqdm import tqdm
import argpar<PERSON>

def get_label_filename(ct_filename, dataset_type):
    """
    Get corresponding label filename based on CT filename and dataset type.
    
    Args:
        ct_filename (str): CT image filename
        dataset_type (str): '05_AbdomenAtlas' or '06_FLARE2023'
        
    Returns:
        str: Corresponding label filename
    """
    if dataset_type == "05_AbdomenAtlas":
        # Same filename for AbdomenAtlas
        return ct_filename
    elif dataset_type == "06_FLARE2023":
        # Remove _0000 suffix for FLARE2023
        if ct_filename.startswith("FLARE23_") and ct_filename.endswith("_0000.nii.gz"):
            # Extract number from FLARE23_XXXX_0000.nii.gz
            parts = ct_filename.split("_")
            number = int(parts[1])  # Remove leading zeros
            return f"FLARE23_{number}.nii.gz"
    return None

def check_affine_difference(affine1, affine2, threshold=1e-2):
    """
    Check if two affine matrices are significantly different.
    
    Args:
        affine1, affine2: 4x4 affine matrices
        threshold: Maximum acceptable difference
        
    Returns:
        tuple: (is_acceptable, max_difference, difference_matrix)
    """
    diff = np.abs(affine1 - affine2)
    max_diff = np.max(diff)
    
    # Check if difference is acceptable
    is_acceptable = max_diff < threshold
    
    return is_acceptable, max_diff, diff

def fix_label_affine(ct_path, label_path, backup_dir=None):
    """
    Fix label affine matrix by replacing it with CT affine matrix.
    
    Args:
        ct_path (str): Path to CT image
        label_path (str): Path to label image
        backup_dir (str): Directory to save backup (optional)
        
    Returns:
        tuple: (success, message, max_difference)
    """
    try:
        # Load CT image
        ct_img = nib.load(ct_path)
        ct_affine = ct_img.affine
        
        # Load label image
        label_img = nib.load(label_path)
        label_affine = label_img.affine
        label_data = label_img.get_fdata()
        
        # Check if shapes match
        if ct_img.shape != label_img.shape:
            return False, f"Shape mismatch: CT {ct_img.shape} vs Label {label_img.shape}", None
        
        # Check affine difference
        is_acceptable, max_diff, diff_matrix = check_affine_difference(ct_affine, label_affine)
        
        if max_diff > 0.1:  # Very large difference threshold
            return False, f"Affine difference too large: {max_diff:.6f}", max_diff
        
        # Create backup if requested
        if backup_dir and max_diff > 1e-6:  # Only backup if there's actually a difference
            os.makedirs(backup_dir, exist_ok=True)
            backup_path = os.path.join(backup_dir, os.path.basename(label_path))
            if not os.path.exists(backup_path):
                nib.save(label_img, backup_path)
        
        # Create new label image with CT affine
        if max_diff > 1e-6:  # Only save if there's actually a difference
            new_label_img = nib.Nifti1Image(label_data, ct_affine, label_img.header)
            nib.save(new_label_img, label_path)
            return True, f"Fixed affine (diff: {max_diff:.6f})", max_diff
        else:
            return True, f"No change needed (diff: {max_diff:.6f})", max_diff
            
    except Exception as e:
        return False, f"Error: {str(e)}", None

def main():
    parser = argparse.ArgumentParser(description='Fix label affine matrices')
    parser.add_argument('--ct_dir_05', 
                       default='/home/<USER>/DiffTumor/data/HealthyCT/CT/05_AbdomenAtlas',
                       help='AbdomenAtlas CT directory')
    parser.add_argument('--ct_dir_06', 
                       default='/home/<USER>/DiffTumor/data/HealthyCT/CT/06_FLARE2023',
                       help='FLARE2023 CT directory')
    parser.add_argument('--label_dir', 
                       default='/home/<USER>/DiffTumor/data/HealthyCT/label/liver_label',
                       help='Label directory')
    parser.add_argument('--backup_dir', 
                       default='/home/<USER>/DiffTumor/data/HealthyCT/label/liver_label_backup',
                       help='Backup directory for original labels')
    parser.add_argument('--dry_run', action='store_true',
                       help='Only check differences without making changes')
    
    args = parser.parse_args()
    
    print("Fixing label affine matrices...")
    print(f"CT directories: {args.ct_dir_05}, {args.ct_dir_06}")
    print(f"Label directory: {args.label_dir}")
    print(f"Backup directory: {args.backup_dir}")
    print(f"Dry run: {args.dry_run}")
    print("=" * 80)
    
    results = {
        'processed': 0,
        'fixed': 0,
        'no_change': 0,
        'errors': 0,
        'large_differences': [],
        'error_files': []
    }
    
    # Process AbdomenAtlas files
    print("\nProcessing AbdomenAtlas files...")
    ct_files_05 = glob.glob(os.path.join(args.ct_dir_05, "*.nii.gz"))
    ct_files_05.sort()
    
    for ct_file in tqdm(ct_files_05, desc="AbdomenAtlas"):
        ct_filename = os.path.basename(ct_file)
        label_filename = get_label_filename(ct_filename, "05_AbdomenAtlas")
        label_path = os.path.join(args.label_dir, label_filename)
        
        if not os.path.exists(label_path):
            results['error_files'].append(f"Label not found: {label_filename}")
            results['errors'] += 1
            continue
        
        if args.dry_run:
            # Just check differences
            try:
                ct_img = nib.load(ct_file)
                label_img = nib.load(label_path)
                is_acceptable, max_diff, _ = check_affine_difference(ct_img.affine, label_img.affine)
                if max_diff > 1e-6:
                    print(f"  {ct_filename}: difference = {max_diff:.6f}")
                    if max_diff > 1e-3:
                        results['large_differences'].append((ct_filename, max_diff))
            except Exception as e:
                results['error_files'].append(f"{ct_filename}: {str(e)}")
        else:
            success, message, max_diff = fix_label_affine(ct_file, label_path, args.backup_dir)
            results['processed'] += 1
            
            if success:
                if "Fixed" in message:
                    results['fixed'] += 1
                else:
                    results['no_change'] += 1
                    
                if max_diff and max_diff > 1e-3:
                    results['large_differences'].append((ct_filename, max_diff))
            else:
                results['errors'] += 1
                results['error_files'].append(f"{ct_filename}: {message}")
    
    # Process FLARE2023 files
    print("\nProcessing FLARE2023 files...")
    ct_files_06 = glob.glob(os.path.join(args.ct_dir_06, "*.nii.gz"))
    ct_files_06.sort()
    
    for ct_file in tqdm(ct_files_06, desc="FLARE2023"):
        ct_filename = os.path.basename(ct_file)
        label_filename = get_label_filename(ct_filename, "06_FLARE2023")
        
        if not label_filename:
            results['error_files'].append(f"Cannot parse filename: {ct_filename}")
            results['errors'] += 1
            continue
            
        label_path = os.path.join(args.label_dir, label_filename)
        
        if not os.path.exists(label_path):
            results['error_files'].append(f"Label not found: {label_filename}")
            results['errors'] += 1
            continue
        
        if args.dry_run:
            # Just check differences
            try:
                ct_img = nib.load(ct_file)
                label_img = nib.load(label_path)
                is_acceptable, max_diff, _ = check_affine_difference(ct_img.affine, label_img.affine)
                if max_diff > 1e-6:
                    print(f"  {ct_filename} -> {label_filename}: difference = {max_diff:.6f}")
                    if max_diff > 1e-3:
                        results['large_differences'].append((f"{ct_filename}->{label_filename}", max_diff))
            except Exception as e:
                results['error_files'].append(f"{ct_filename}: {str(e)}")
        else:
            success, message, max_diff = fix_label_affine(ct_file, label_path, args.backup_dir)
            results['processed'] += 1
            
            if success:
                if "Fixed" in message:
                    results['fixed'] += 1
                else:
                    results['no_change'] += 1
                    
                if max_diff and max_diff > 1e-3:
                    results['large_differences'].append((f"{ct_filename}->{label_filename}", max_diff))
            else:
                results['errors'] += 1
                results['error_files'].append(f"{ct_filename}->{label_filename}: {message}")
    
    # Print summary
    print("\n" + "=" * 80)
    print("SUMMARY")
    print("=" * 80)
    
    if args.dry_run:
        print("DRY RUN - No changes made")
    else:
        print(f"Total processed: {results['processed']}")
        print(f"Fixed: {results['fixed']}")
        print(f"No change needed: {results['no_change']}")
    
    print(f"Errors: {results['errors']}")
    
    if results['large_differences']:
        print(f"\nFiles with large differences (>1e-3):")
        for filename, diff in results['large_differences']:
            print(f"  {filename}: {diff:.6f}")
    
    if results['error_files']:
        print(f"\nError files:")
        for error in results['error_files'][:10]:  # Show first 10 errors
            print(f"  {error}")
        if len(results['error_files']) > 10:
            print(f"  ... and {len(results['error_files']) - 10} more errors")

if __name__ == "__main__":
    main()
