#!/usr/bin/env python3
"""
Script to extract liver labels (label value = 1) from FLARE2023 dataset labels.
This script processes all label files in the labelsTr2200 directory and creates
binary liver masks where only liver regions (label=1) are preserved.
"""

import os
import glob
import nibabel as nib
import numpy as np
from tqdm import tqdm
import argparse

def extract_liver_label(input_path, output_path):
    """
    Extract liver label (value=1) from a multi-label segmentation file.
    
    Args:
        input_path (str): Path to input label file
        output_path (str): Path to save the liver-only label file
    """
    try:
        # Load the NIfTI file
        nii_img = nib.load(input_path)
        label_data = nii_img.get_fdata()
        
        # Create binary liver mask (label=1 becomes 1, everything else becomes 0)
        liver_mask = (label_data == 1).astype(np.uint8)
        
        # Create new NIfTI image with the same header and affine
        liver_nii = nib.Nifti1Image(liver_mask, nii_img.affine, nii_img.header)
        
        # Save the liver mask
        nib.save(liver_nii, output_path)
        
        return True
        
    except Exception as e:
        print(f"Error processing {input_path}: {str(e)}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Extract liver labels from FLARE2023 dataset')
    parser.add_argument('--input_dir', 
                       default='data/Dataset005_FLARE2023/labelsTr2200/labelsTr2200',
                       help='Input directory containing label files')
    parser.add_argument('--output_dir', 
                       default='data/Dataset005_FLARE2023/labelsTr2200/labelsTr2200_liver',
                       help='Output directory for liver-only labels')
    
    args = parser.parse_args()
    
    input_dir = args.input_dir
    output_dir = args.output_dir
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Find all .nii.gz files in the input directory
    label_files = glob.glob(os.path.join(input_dir, "*.nii.gz"))
    
    if not label_files:
        print(f"No .nii.gz files found in {input_dir}")
        return
    
    print(f"Found {len(label_files)} label files to process")
    print(f"Input directory: {input_dir}")
    print(f"Output directory: {output_dir}")
    
    # Process each file
    successful = 0
    failed = 0
    
    for label_file in tqdm(label_files, desc="Processing labels"):
        # Get the filename without path
        filename = os.path.basename(label_file)
        
        # Create output path
        output_path = os.path.join(output_dir, filename)
        
        # Skip if output file already exists
        if os.path.exists(output_path):
            print(f"Skipping {filename} - already exists")
            continue
        
        # Extract liver label
        if extract_liver_label(label_file, output_path):
            successful += 1
        else:
            failed += 1
    
    print(f"\nProcessing completed!")
    print(f"Successfully processed: {successful} files")
    print(f"Failed: {failed} files")
    
    # Verify some results
    if successful > 0:
        print(f"\nOutput files saved to: {output_dir}")
        output_files = glob.glob(os.path.join(output_dir, "*.nii.gz"))
        print(f"Total output files: {len(output_files)}")

if __name__ == "__main__":
    main()
