#!/usr/bin/env python3
"""
验证两个文件的affine矩阵是否完全相同
"""

import nibabel as nib
import numpy as np

def verify_affine_exact(image_path, label_path):
    """验证两个文件的affine矩阵是否完全相同"""
    try:
        # 加载文件
        image_nii = nib.load(image_path)
        label_nii = nib.load(label_path)
        
        image_affine = image_nii.affine
        label_affine = label_nii.affine
        
        print("Image affine:")
        print(image_affine)
        print("\nLabel affine:")
        print(label_affine)
        
        # 检查是否完全相同（逐元素比较）
        exact_match = np.array_equal(image_affine, label_affine)
        print(f"\n完全相同（逐元素比较）: {exact_match}")
        
        if not exact_match:
            # 计算差异
            diff = image_affine - label_affine
            max_diff = np.max(np.abs(diff))
            print(f"最大差异: {max_diff}")
            print("差异矩阵:")
            print(diff)
            
            # 检查在不同容差下是否相同
            tolerances = [1e-15, 1e-12, 1e-10, 1e-8, 1e-6]
            for tol in tolerances:
                close = np.allclose(image_affine, label_affine, rtol=tol, atol=tol)
                print(f"容差 {tol}: {close}")
        else:
            print("✓ Affine矩阵完全相同！")
            
        return exact_match
        
    except Exception as e:
        print(f"验证失败: {str(e)}")
        return False

if __name__ == "__main__":
    image_path = "/home/<USER>/DiffTumor/data/HealthyCT/CT/05_AbdomenAtlas/AbdomenAtlasMini_00002385.nii.gz"
    label_path = "/home/<USER>/DiffTumor/data/HealthyCT/label/liver_label/AbdomenAtlasMini_00002385.nii.gz"
    
    print("=" * 80)
    print("验证Affine矩阵")
    print("=" * 80)
    
    verify_affine_exact(image_path, label_path)
